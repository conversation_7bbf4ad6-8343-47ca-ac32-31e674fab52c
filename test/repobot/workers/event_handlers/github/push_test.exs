defmodule Repobot.Workers.EventHandlers.GitHub.PushTest do
  use Repobot.DataCase, async: true
  use Oban.Testing, repo: Repobot.Repo

  import Repobot.Test.Fixtures
  import Mox

  alias Repobot.{Events, Repo}
  alias Repobot.Events.Event
  alias Repobot.Workers.EventHandlers.GitHub.Push

  # Make sure mocks are set up for each test
  setup :set_mox_from_context
  setup :verify_on_exit!

  setup do
    user = create_user()
    %{user: user}
  end

  describe "handle/1" do
    test "handles exceptions properly with detailed logging", %{user: user} do
      # Create a repository
      repo =
        create_repository(%{
          template: true,
          name: "test-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Create push event payload
      payload = %{
        "ref" => "refs/heads/main",
        "repository" => %{
          "full_name" => repo.full_name,
          "default_branch" => "main"
        },
        "commits" => [
          %{
            "id" => "abc123",
            "message" => "Update config",
            "added" => [],
            "modified" => ["some/path.ex"]
          }
        ]
      }

      # Create the event
      {:ok, event} =
        Events.create_event(%{
          type: "github.push",
          payload: payload,
          organization_id: user.default_organization_id,
          repository_id: repo.id,
          status: "pending"
        })

      # Mock GitHub API to raise an exception
      Repobot.Test.GitHubMock
      |> expect(:client, fn _user -> :test_client end)
      |> expect(:get_file_content, fn :test_client, _owner, _repo_name, _path ->
        raise RuntimeError, "Simulated GitHub API error"
      end)

      # The job should now crash with the exception instead of swallowing it
      assert_raise RuntimeError, "Simulated GitHub API error", fn ->
        perform_job(Push, %{"event_id" => event.id})
      end

      # Since the job crashed, the event status should still be pending
      # (the EventHandler won't get a chance to update it to failed)
      updated_event = Events.get_event(event.id)
      assert updated_event.status == "pending"
    end

    test "processes push event and logs to events table", %{user: user} do
      # Create a repository
      repo =
        create_repository(%{
          template: true,
          name: "test-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Create push event payload
      payload = %{
        "ref" => "refs/heads/main",
        "repository" => %{
          "full_name" => repo.full_name,
          "default_branch" => "main"
        },
        "commits" => [
          %{
            "id" => "abc123",
            "message" => "Update config",
            "added" => [],
            "modified" => ["some/path.ex"]
          }
        ]
      }

      # Create the event
      {:ok, event} =
        Events.create_event(%{
          type: "github.push",
          payload: payload,
          organization_id: user.default_organization_id,
          repository_id: repo.id
        })

      # Mock GitHub API for repository file refresh
      Repobot.Test.GitHubMock
      |> expect(:client, fn _user -> :test_client end)
      |> expect(:get_file_content, fn :test_client, owner, repo_name, "some/path.ex" ->
        if owner == repo.owner and repo_name == repo.name do
          {:ok, "test content", %{"sha" => "abc123", "size" => 12}}
        else
          {:error, "not found"}
        end
      end)

      # Perform the job
      assert :ok = perform_job(Push, %{"event_id" => event.id})

      # Verify worker success event was logged
      success_events = Repo.all(from e in Event, where: e.type == "repobot.worker.success")
      assert length(success_events) > 0

      # Verify repository refresh success event was logged
      refresh_events = Repo.all(from e in Event, where: e.type == "repobot.repository_refresh.success")
      assert length(refresh_events) > 0

      latest_refresh_event = Enum.max_by(refresh_events, & &1.inserted_at)
      assert latest_refresh_event.organization_id == repo.organization_id
      assert latest_refresh_event.payload["repository_id"] == repo.id
      assert latest_refresh_event.payload["trigger"] == "push_webhook"
    end

    test "handles push to template repository and creates sync events", %{user: user} do
      # Create template and target repositories
      template_repo =
        create_repository(%{
          template: true,
          name: "template-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      target_repo =
        create_repository(%{
          name: "target-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Create source file
      source_file =
        create_source_file(%{
          name: "config.ex",
          target_path: "config/config.ex",
          content: "old content",
          source_repository_id: template_repo.id,
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Associate source file with both repos
      Repo.insert!(%Repobot.RepositorySourceFile{
        repository_id: template_repo.id,
        source_file_id: source_file.id
      })

      Repo.insert!(%Repobot.RepositorySourceFile{
        repository_id: target_repo.id,
        source_file_id: source_file.id
      })

      # Create push event payload
      payload = %{
        "ref" => "refs/heads/main",
        "repository" => %{
          "full_name" => template_repo.full_name,
          "default_branch" => "main"
        },
        "commits" => [
          %{
            "id" => "abc123",
            "message" => "Update config",
            "added" => [],
            "modified" => ["config/config.ex"]
          }
        ]
      }

      # Create the event
      {:ok, event} =
        Events.create_event(%{
          type: "github.push",
          payload: payload,
          organization_id: user.default_organization_id,
          repository_id: template_repo.id,
          status: "pending"
        })

      # Mock GitHub API calls
      Repobot.Test.GitHubMock
      |> expect(:client, 2, fn owner, repo ->
        cond do
          owner == template_repo.owner and repo == template_repo.name -> :template_client
          owner == target_repo.owner and repo == target_repo.name -> :target_client
        end
      end)
      |> expect(:client, fn _user -> :user_client end)
      |> expect(:get_file_content, fn :template_client, owner, repo, path, commit_sha ->
        if owner == template_repo.owner and repo == template_repo.name and
             path == "config/config.ex" and commit_sha == "abc123" do
          {:ok, "new content", %{"sha" => "new-sha"}}
        else
          raise "Unexpected get_file_content call: #{inspect({owner, repo, path, commit_sha})}"
        end
      end)
      |> expect(:get_file_content, fn :user_client, owner, repo_name, "config/config.ex" ->
        if owner == template_repo.owner and repo_name == template_repo.name do
          {:ok, "new content", %{"sha" => "abc123", "size" => 11}}
        else
          {:error, "not found"}
        end
      end)

      # Mock the sync backend for the sync events that will be created
      Repobot.Test.SyncMock
      |> expect(:sync_changes, fn source_files,
                                  template_repo_arg,
                                  target_repo_arg,
                                  :target_client,
                                  opts ->
        # Verify arguments
        assert length(source_files) == 1
        assert List.first(source_files).id == source_file.id
        assert template_repo_arg.id == template_repo.id
        assert target_repo_arg.id == target_repo.id
        assert opts[:commit_message] == "Update config"

        {:ok, "Files updated successfully"}
      end)

      # Count events before processing
      _event_count_before = length(Repo.all(Event))

      # Perform the job
      assert :ok = perform_job(Push, %{"event_id" => event.id})

      # Verify the event status was updated
      updated_event = Events.get_event(event.id)
      assert updated_event.status == "completed"

      # Verify sync events were created
      sync_events = Repo.all(from e in Event, where: e.type == "repobot.sync")

      assert length(sync_events) > 0

      # Find the original sync event (not the result event)
      # The original sync event should have the triggered_by_event_id field
      original_sync_event =
        Enum.find(sync_events, fn sync_event ->
          Map.has_key?(sync_event.payload, "triggered_by_event_id")
        end)

      assert original_sync_event != nil, "Should find a sync event with triggered_by_event_id"

      assert original_sync_event.organization_id == target_repo.organization_id
      assert original_sync_event.payload["template_repository_id"] == template_repo.id
      assert original_sync_event.payload["target_repository_id"] == target_repo.id
      assert original_sync_event.payload["triggered_by_event_id"] == event.id

      # Verify source file was updated
      updated_source_file = Repo.get(Repobot.SourceFile, source_file.id)
      assert updated_source_file.content == "new content"
    end

    test "ignores push to non-default branch", %{user: user} do
      # Create template repository
      template_repo =
        create_repository(%{
          template: true,
          name: "template-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Create push event payload for non-default branch
      payload = %{
        "ref" => "refs/heads/feature",
        "repository" => %{
          "full_name" => template_repo.full_name,
          "default_branch" => "main"
        },
        "commits" => [
          %{
            "id" => "abc123",
            "message" => "Update config",
            "modified" => ["config/config.ex"]
          }
        ]
      }

      # Create the event
      {:ok, event} =
        Events.create_event(%{
          type: "github.push",
          payload: payload,
          organization_id: user.default_organization_id,
          repository_id: template_repo.id,
          status: "pending"
        })

      # Perform the job
      assert :ok = perform_job(Push, %{"event_id" => event.id})

      # Verify the event status was updated
      updated_event = Events.get_event(event.id)
      assert updated_event.status == "completed"

      # Verify no sync events were created
      sync_events = Repo.all(from e in Event, where: e.type == "repobot.sync")
      assert length(sync_events) == 0
    end

    test "handles push to template repository with multiple target repositories", %{user: user} do
      # Create template repository
      template_repo =
        create_repository(%{
          template: true,
          name: "template-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Create multiple target repositories
      target_repo_1 =
        create_repository(%{
          name: "target-repo-1",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      target_repo_2 =
        create_repository(%{
          name: "target-repo-2",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Create source file
      source_file =
        create_source_file(%{
          name: "config.ex",
          target_path: "config/config.ex",
          content: "old content",
          source_repository_id: template_repo.id,
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Associate source file with template and both target repositories
      Repo.insert!(%Repobot.RepositorySourceFile{
        repository_id: template_repo.id,
        source_file_id: source_file.id
      })

      Repo.insert!(%Repobot.RepositorySourceFile{
        repository_id: target_repo_1.id,
        source_file_id: source_file.id
      })

      Repo.insert!(%Repobot.RepositorySourceFile{
        repository_id: target_repo_2.id,
        source_file_id: source_file.id
      })

      # Create push event payload
      payload = %{
        "ref" => "refs/heads/main",
        "repository" => %{
          "full_name" => template_repo.full_name,
          "default_branch" => "main"
        },
        "commits" => [
          %{
            "id" => "abc123",
            "message" => "Update config",
            "added" => [],
            "modified" => ["config/config.ex"]
          }
        ]
      }

      # Create the event
      {:ok, event} =
        Events.create_event(%{
          type: "github.push",
          payload: payload,
          organization_id: user.default_organization_id,
          repository_id: template_repo.id,
          status: "pending"
        })

      # Mock GitHub API calls
      Repobot.Test.GitHubMock
      |> expect(:client, 3, fn owner, repo ->
        cond do
          owner == template_repo.owner and repo == template_repo.name -> :template_client
          owner == target_repo_1.owner and repo == target_repo_1.name -> :target_client_1
          owner == target_repo_2.owner and repo == target_repo_2.name -> :target_client_2
        end
      end)
      |> expect(:client, fn _user -> :user_client end)
      |> expect(:get_file_content, fn :template_client, owner, repo, path, commit_sha ->
        if owner == template_repo.owner and repo == template_repo.name and
             path == "config/config.ex" and commit_sha == "abc123" do
          {:ok, "new content", %{"sha" => "new-sha"}}
        else
          raise "Unexpected get_file_content call: #{inspect({owner, repo, path, commit_sha})}"
        end
      end)
      |> expect(:get_file_content, fn :user_client, owner, repo_name, "config/config.ex" ->
        if owner == template_repo.owner and repo_name == template_repo.name do
          {:ok, "new content", %{"sha" => "abc123", "size" => 11}}
        else
          {:error, "not found"}
        end
      end)

      # Mock the sync backend for both target repositories
      Repobot.Test.SyncMock
      |> expect(:sync_changes, 2, fn source_files,
                                     template_repo_arg,
                                     target_repo_arg,
                                     client,
                                     opts ->
        # Verify arguments
        assert length(source_files) == 1
        assert List.first(source_files).id == source_file.id
        assert template_repo_arg.id == template_repo.id
        assert target_repo_arg.id in [target_repo_1.id, target_repo_2.id]
        assert client in [:target_client_1, :target_client_2]
        assert opts[:commit_message] == "Update config"

        {:ok, "Files updated successfully"}
      end)

      # Perform the job
      assert :ok = perform_job(Push, %{"event_id" => event.id})

      # Verify the event status was updated
      updated_event = Events.get_event(event.id)
      assert updated_event.status == "completed"

      # Verify sync events were created for both target repositories
      sync_events = Repo.all(from e in Event, where: e.type == "repobot.sync")

      # Should have 2 original sync events (one for each target repo) plus potentially result events
      original_sync_events =
        Enum.filter(sync_events, fn sync_event ->
          Map.has_key?(sync_event.payload, "triggered_by_event_id")
        end)

      assert length(original_sync_events) == 2

      # Verify both target repositories have sync events
      target_repo_ids =
        Enum.map(original_sync_events, fn event ->
          event.payload["target_repository_id"]
        end)

      assert target_repo_1.id in target_repo_ids
      assert target_repo_2.id in target_repo_ids

      # Verify all sync events reference the original push event
      Enum.each(original_sync_events, fn sync_event ->
        assert sync_event.payload["template_repository_id"] == template_repo.id
        assert sync_event.payload["triggered_by_event_id"] == event.id
      end)

      # Verify source file was updated
      updated_source_file = Repo.get(Repobot.SourceFile, source_file.id)
      assert updated_source_file.content == "new content"
    end

    test "handles push to non-template repository", %{user: user} do
      # Create non-template repository
      repo =
        create_repository(%{
          template: false,
          name: "normal-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Create push event payload
      payload = %{
        "ref" => "refs/heads/main",
        "repository" => %{
          "full_name" => repo.full_name,
          "default_branch" => "main"
        },
        "commits" => [
          %{
            "id" => "abc123",
            "message" => "Update config",
            "modified" => ["config/config.ex"]
          }
        ]
      }

      # Create the event
      {:ok, event} =
        Events.create_event(%{
          type: "github.push",
          payload: payload,
          organization_id: user.default_organization_id,
          repository_id: repo.id,
          status: "pending"
        })

      # Mock GitHub API for repository file refresh
      Repobot.Test.GitHubMock
      |> expect(:client, fn _user -> :test_client end)
      |> expect(:get_file_content, fn :test_client, owner, repo_name, "config/config.ex" ->
        if owner == repo.owner and repo_name == repo.name do
          {:ok, "test content", %{"sha" => "abc123", "size" => 12}}
        else
          {:error, "not found"}
        end
      end)

      # Perform the job
      assert :ok = perform_job(Push, %{"event_id" => event.id})

      # Verify the event status was updated
      updated_event = Events.get_event(event.id)
      assert updated_event.status == "completed"

      # Verify no sync events were created (since it's not a template repository)
      sync_events = Repo.all(from e in Event, where: e.type == "repobot.sync")
      assert length(sync_events) == 0
    end

    test "handles push to template repository with template files (.liquid)", %{user: user} do
      # Create template and target repositories
      template_repo =
        create_repository(%{
          template: true,
          name: "template-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      target_repo =
        create_repository(%{
          name: "target-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Create template source file (is_template: true)
      source_file =
        create_source_file(%{
          name: "config.ex.liquid",
          target_path: "config/config.ex",
          content: "old template content",
          is_template: true,
          source_repository_id: template_repo.id,
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Associate source file with both repos
      Repo.insert!(%Repobot.RepositorySourceFile{
        repository_id: template_repo.id,
        source_file_id: source_file.id
      })

      Repo.insert!(%Repobot.RepositorySourceFile{
        repository_id: target_repo.id,
        source_file_id: source_file.id
      })

      # Create push event payload - note the .liquid file is modified
      payload = %{
        "ref" => "refs/heads/main",
        "repository" => %{
          "full_name" => template_repo.full_name,
          "default_branch" => "main"
        },
        "commits" => [
          %{
            "id" => "abc123",
            "message" => "Update template",
            "added" => [],
            "modified" => ["config/config.ex.liquid"]
          }
        ]
      }

      # Create the event
      {:ok, event} =
        Events.create_event(%{
          type: "github.push",
          payload: payload,
          organization_id: user.default_organization_id,
          repository_id: template_repo.id,
          status: "pending"
        })

      # Mock GitHub API calls
      Repobot.Test.GitHubMock
      |> expect(:client, 2, fn owner, repo ->
        cond do
          owner == template_repo.owner and repo == template_repo.name -> :template_client
          owner == target_repo.owner and repo == target_repo.name -> :target_client
        end
      end)
      |> expect(:client, fn _user -> :user_client end)
      |> expect(:get_file_content, fn :template_client, owner, repo, path, commit_sha ->
        if owner == template_repo.owner and repo == template_repo.name and
             path == "config/config.ex.liquid" and commit_sha == "abc123" do
          {:ok, "new template content", %{"sha" => "new-sha"}}
        else
          raise "Unexpected get_file_content call: #{inspect({owner, repo, path, commit_sha})}"
        end
      end)
      |> expect(:get_file_content, fn :user_client, owner, repo_name, "config/config.ex.liquid" ->
        if owner == template_repo.owner and repo_name == template_repo.name do
          {:ok, "new template content", %{"sha" => "abc123", "size" => 20}}
        else
          {:error, "not found"}
        end
      end)

      # Mock the sync backend
      Repobot.Test.SyncMock
      |> expect(:sync_changes, fn source_files,
                                  template_repo_arg,
                                  target_repo_arg,
                                  :target_client,
                                  opts ->
        # Verify arguments
        assert length(source_files) == 1
        source_file_arg = List.first(source_files)
        assert source_file_arg.id == source_file.id
        assert source_file_arg.is_template == true
        assert template_repo_arg.id == template_repo.id
        assert target_repo_arg.id == target_repo.id
        assert opts[:commit_message] == "Update template"

        {:ok, "Template files updated successfully"}
      end)

      # Perform the job
      assert :ok = perform_job(Push, %{"event_id" => event.id})

      # Verify the event status was updated
      updated_event = Events.get_event(event.id)
      assert updated_event.status == "completed"

      # Verify sync events were created
      sync_events = Repo.all(from e in Event, where: e.type == "repobot.sync")
      assert length(sync_events) > 0

      # Verify source file was updated
      updated_source_file = Repo.get(Repobot.SourceFile, source_file.id)
      assert updated_source_file.content == "new template content"
    end

    test "handles push to template repository with multiple source files", %{user: user} do
      # Create template and target repositories
      template_repo =
        create_repository(%{
          template: true,
          name: "template-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      target_repo =
        create_repository(%{
          name: "target-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Create multiple source files
      source_file_1 =
        create_source_file(%{
          name: "config.ex",
          target_path: "config/config.ex",
          content: "old config content",
          source_repository_id: template_repo.id,
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      source_file_2 =
        create_source_file(%{
          name: "README.md",
          target_path: "README.md",
          content: "old readme content",
          source_repository_id: template_repo.id,
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Associate both source files with both repos
      for source_file <- [source_file_1, source_file_2] do
        Repo.insert!(%Repobot.RepositorySourceFile{
          repository_id: template_repo.id,
          source_file_id: source_file.id
        })

        Repo.insert!(%Repobot.RepositorySourceFile{
          repository_id: target_repo.id,
          source_file_id: source_file.id
        })
      end

      # Create push event payload with multiple modified files
      payload = %{
        "ref" => "refs/heads/main",
        "repository" => %{
          "full_name" => template_repo.full_name,
          "default_branch" => "main"
        },
        "commits" => [
          %{
            "id" => "abc123",
            "message" => "Update multiple files",
            "added" => [],
            "modified" => ["config/config.ex", "README.md"]
          }
        ]
      }

      # Create the event
      {:ok, event} =
        Events.create_event(%{
          type: "github.push",
          payload: payload,
          organization_id: user.default_organization_id,
          repository_id: template_repo.id,
          status: "pending"
        })

      # Mock GitHub API calls
      Repobot.Test.GitHubMock
      |> expect(:client, 2, fn owner, repo ->
        cond do
          owner == template_repo.owner and repo == template_repo.name -> :template_client
          owner == target_repo.owner and repo == target_repo.name -> :target_client
        end
      end)
      |> expect(:client, fn _user -> :user_client end)
      |> expect(:get_file_content, 2, fn :template_client, owner, repo, path, commit_sha ->
        if owner == template_repo.owner and repo == template_repo.name and commit_sha == "abc123" do
          case path do
            "config/config.ex" -> {:ok, "new config content", %{"sha" => "new-sha-1"}}
            "README.md" -> {:ok, "new readme content", %{"sha" => "new-sha-2"}}
            _ -> raise "Unexpected path: #{path}"
          end
        else
          raise "Unexpected get_file_content call: #{inspect({owner, repo, path, commit_sha})}"
        end
      end)
      |> expect(:get_file_content, 2, fn :user_client, owner, repo_name, path ->
        if owner == template_repo.owner and repo_name == template_repo.name do
          case path do
            "config/config.ex" -> {:ok, "new config content", %{"sha" => "abc123", "size" => 18}}
            "README.md" -> {:ok, "new readme content", %{"sha" => "abc123", "size" => 18}}
            _ -> {:error, "not found"}
          end
        else
          {:error, "not found"}
        end
      end)

      # Mock the sync backend
      Repobot.Test.SyncMock
      |> expect(:sync_changes, fn source_files,
                                  template_repo_arg,
                                  target_repo_arg,
                                  :target_client,
                                  opts ->
        # Verify arguments
        assert length(source_files) == 2
        source_file_ids = Enum.map(source_files, & &1.id)
        assert source_file_1.id in source_file_ids
        assert source_file_2.id in source_file_ids
        assert template_repo_arg.id == template_repo.id
        assert target_repo_arg.id == target_repo.id
        assert opts[:commit_message] == "Update multiple files"

        {:ok, "Multiple files updated successfully"}
      end)

      # Perform the job
      assert :ok = perform_job(Push, %{"event_id" => event.id})

      # Verify the event status was updated
      updated_event = Events.get_event(event.id)
      assert updated_event.status == "completed"

      # Verify sync events were created
      sync_events = Repo.all(from e in Event, where: e.type == "repobot.sync")
      assert length(sync_events) > 0

      # Verify both source files were updated
      updated_source_file_1 = Repo.get(Repobot.SourceFile, source_file_1.id)
      assert updated_source_file_1.content == "new config content"

      updated_source_file_2 = Repo.get(Repobot.SourceFile, source_file_2.id)
      assert updated_source_file_2.content == "new readme content"
    end

    test "handles GitHub API error when fetching source file content", %{user: user} do
      # Create template and target repositories
      template_repo =
        create_repository(%{
          template: true,
          name: "template-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      target_repo =
        create_repository(%{
          name: "target-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Create source file
      source_file =
        create_source_file(%{
          name: "config.ex",
          target_path: "config/config.ex",
          content: "old content",
          source_repository_id: template_repo.id,
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Associate source file with both repos
      Repo.insert!(%Repobot.RepositorySourceFile{
        repository_id: template_repo.id,
        source_file_id: source_file.id
      })

      Repo.insert!(%Repobot.RepositorySourceFile{
        repository_id: target_repo.id,
        source_file_id: source_file.id
      })

      # Create push event payload
      payload = %{
        "ref" => "refs/heads/main",
        "repository" => %{
          "full_name" => template_repo.full_name,
          "default_branch" => "main"
        },
        "commits" => [
          %{
            "id" => "abc123",
            "message" => "Update config",
            "added" => [],
            "modified" => ["config/config.ex"]
          }
        ]
      }

      # Create the event
      {:ok, event} =
        Events.create_event(%{
          type: "github.push",
          payload: payload,
          organization_id: user.default_organization_id,
          repository_id: template_repo.id,
          status: "pending"
        })

      # Mock GitHub API calls - simulate error when fetching file content
      Repobot.Test.GitHubMock
      |> expect(:client, fn owner, repo ->
        if owner == template_repo.owner and repo == template_repo.name do
          :template_client
        end
      end)
      |> expect(:client, fn _user -> :user_client end)
      |> expect(:get_file_content, fn :template_client, owner, repo, path, commit_sha ->
        if owner == template_repo.owner and repo == template_repo.name and
             path == "config/config.ex" and commit_sha == "abc123" do
          {:error, "File not found"}
        else
          raise "Unexpected get_file_content call: #{inspect({owner, repo, path, commit_sha})}"
        end
      end)
      |> expect(:get_file_content, fn :user_client, owner, repo_name, "config/config.ex" ->
        if owner == template_repo.owner and repo_name == template_repo.name do
          {:ok, "old content", %{"sha" => "abc123", "size" => 11}}
        else
          {:error, "not found"}
        end
      end)

      # Perform the job - expect an error due to file fetch failure
      assert {:error, error_message} = perform_job(Push, %{"event_id" => event.id})
      assert error_message =~ "Skipped sync for commit abc123 due to file fetch errors"
      assert error_message =~ "Failed to get file content"

      # Verify the event status was updated to failed
      updated_event = Events.get_event(event.id)
      assert updated_event.status == "failed"

      # Verify no sync events were created due to the error
      sync_events = Repo.all(from e in Event, where: e.type == "repobot.sync")
      assert length(sync_events) == 0

      # Verify source file content was not updated
      updated_source_file = Repo.get(Repobot.SourceFile, source_file.id)
      assert updated_source_file.content == "old content"
    end

    test "handles push with no matching source files", %{user: user} do
      # Create template repository
      template_repo =
        create_repository(%{
          template: true,
          name: "template-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Create source file but for a different path
      _source_file =
        create_source_file(%{
          name: "other.ex",
          target_path: "lib/other.ex",
          content: "other content",
          source_repository_id: template_repo.id,
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Create push event payload for a file that doesn't match any source files
      payload = %{
        "ref" => "refs/heads/main",
        "repository" => %{
          "full_name" => template_repo.full_name,
          "default_branch" => "main"
        },
        "commits" => [
          %{
            "id" => "abc123",
            "message" => "Update untracked file",
            "added" => [],
            "modified" => ["config/config.ex"]
          }
        ]
      }

      # Create the event
      {:ok, event} =
        Events.create_event(%{
          type: "github.push",
          payload: payload,
          organization_id: user.default_organization_id,
          repository_id: template_repo.id,
          status: "pending"
        })

      # Mock GitHub API for repository file refresh
      Repobot.Test.GitHubMock
      |> expect(:client, fn _user -> :user_client end)
      |> expect(:get_file_content, fn :user_client, owner, repo_name, "config/config.ex" ->
        if owner == template_repo.owner and repo_name == template_repo.name do
          {:ok, "untracked content", %{"sha" => "abc123", "size" => 16}}
        else
          {:error, "not found"}
        end
      end)

      # Perform the job
      assert :ok = perform_job(Push, %{"event_id" => event.id})

      # Verify the event status was updated
      updated_event = Events.get_event(event.id)
      assert updated_event.status == "completed"

      # Verify no sync events were created (no matching source files)
      sync_events = Repo.all(from e in Event, where: e.type == "repobot.sync")
      assert length(sync_events) == 0
    end

    test "can update read-only source files from template repository push", %{user: user} do
      # Create template repository
      template_repo =
        create_repository(%{
          template: true,
          name: "template-repo",
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Create a read-only source file (simulating import from template repository)
      source_file =
        create_source_file(%{
          name: "config.ex",
          target_path: "config/config.ex",
          content: "# Original template config",
          source_repository_id: template_repo.id,
          read_only: true,
          user_id: user.id,
          organization_id: user.default_organization_id
        })

      # Associate source file with template repository
      Repo.insert!(%Repobot.RepositorySourceFile{
        repository_id: template_repo.id,
        source_file_id: source_file.id
      })

      # Create push event payload
      payload = %{
        "ref" => "refs/heads/main",
        "repository" => %{
          "full_name" => template_repo.full_name,
          "default_branch" => "main"
        },
        "commits" => [
          %{
            "id" => "abc123",
            "message" => "Update config",
            "added" => [],
            "modified" => ["config/config.ex"]
          }
        ]
      }

      # Create the event
      {:ok, event} =
        Events.create_event(%{
          type: "github.push",
          payload: payload,
          organization_id: user.default_organization_id,
          repository_id: template_repo.id,
          status: "pending"
        })

      # Mock GitHub API calls
      Repobot.Test.GitHubMock
      |> expect(:client, fn owner, repo ->
        if owner == template_repo.owner and repo == template_repo.name do
          :template_client
        end
      end)
      |> expect(:client, fn _user -> :user_client end)
      |> expect(:get_file_content, fn :template_client, owner, repo, path, commit_sha ->
        if owner == template_repo.owner and repo == template_repo.name and
             path == "config/config.ex" and commit_sha == "abc123" do
          {:ok, "# Updated template config from push", %{"sha" => "new-sha"}}
        else
          raise "Unexpected get_file_content call: #{inspect({owner, repo, path, commit_sha})}"
        end
      end)
      |> expect(:get_file_content, fn :user_client, owner, repo_name, "config/config.ex" ->
        if owner == template_repo.owner and repo_name == template_repo.name do
          {:ok, "# Updated template config from push", %{"sha" => "abc123", "size" => 35}}
        else
          {:error, "not found"}
        end
      end)

      # Perform the job
      assert :ok = perform_job(Push, %{"event_id" => event.id})

      # Verify the event status was updated
      updated_event = Events.get_event(event.id)
      assert updated_event.status == "completed"

      # Verify the read-only source file was updated with new content
      updated_source_file = Repo.get(Repobot.SourceFile, source_file.id)
      assert updated_source_file.content == "# Updated template config from push"
      # Should remain read-only
      assert updated_source_file.read_only == true
    end
  end
end
